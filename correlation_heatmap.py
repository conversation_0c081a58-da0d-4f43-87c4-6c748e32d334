import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.font_manager as fm

# 设置中文字体和学术风格
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 25  # 基础字体大小
import matplotlib.font_manager as fm

# 设置新罗马字体
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False

# 从图片中提取的数据
data = {
    'Method': [
        'Random', 'Gemini-1.5-Flash[1 fps]', 'Gemini-1.5-Pro[1 fps]', 'Gemini-2.0-Flash[1 fps]',
        'GPT-4o-mini[32f]', 'GPT-4o[32f]', 'Qwen-VL-Max-Instruct[32f]', 'LLaVA-NeXT-Video-7B-hf[32f]',
        'Phi-3.5-vision-instruct[32f]', '<PERSON>aroo[64f]', 'Qwen2-VL-2B-Instruct[0.5 fps]',
        'Qwen2-VL-7B-Instruct[0.5 fps]', 'InternVL2-2B[32f]', 'InternVL2-4B[32f]',
        'InternVL2-8B[32f]', 'InternVL2-26B[32f]', 'InternVL2-40B[32f]', 'InternVL2-Llama3-76B[32f]'
    ],
    'Rank': ['-', 4, 3, 5, 6, 2, 1, 3, 2, 1, 5, 4, 11, 10, 9, 8, 7, 6],
    'Avg.': [19.7, 40.5, 42.5, 38.3, 36.5, 43.6, 45.5, 38.6, 38.7, 39.2, 31.9, 36.2, 27.6, 28.1, 28.1, 28.3, 28.4, 28.9],
    'Trajectory Captioning': [18.5, 39.7, 58.6, 47.9, 33.0, 47.6, 44.9, 55.7, 67.0, 27.0, 29.9, 36.5, 19.2, 19.2, 23.4, 24.3, 22.2, 19.5],
    'Sequence Recall': [17.0, 51.8, 61.6, 58.9, 53.6, 58.9, 70.5, 39.3, 57.1, 66.1, 54.5, 50.9, 29.5, 37.5, 23.2, 36.6, 19.6, 38.4],
    'Object Recall': [20.8, 61.7, 65.0, 63.3, 48.3, 65.0, 64.2, 43.3, 57.5, 60.8, 30.8, 47.5, 37.5, 33.3, 35.0, 35.0, 30.8, 37.5],
    'Scene Recall': [13.5, 79.3, 72.1, 75.7, 59.5, 67.6, 75.7, 61.3, 64.9, 69.4, 57.7, 65.8, 55.9, 62.2, 52.3, 61.3, 54.1, 54.1],
    'Start/End Position': [21.8, 61.3, 66.2, 57.0, 56.3, 61.3, 73.9, 40.8, 45.1, 53.5, 24.6, 47.2, 22.5, 24.6, 22.5, 26.8, 21.1, 18.3],
    'Proximity': [37.8, 47.1, 66.4, 66.4, 69.7, 63.0, 78.2, 58.8, 48.7, 75.6, 69.7, 52.1, 57.1, 66.4, 58.0, 51.2, 61.3, 65.5],
    'Duration': [35.6, 59.8, 63.6, 47.7, 51.5, 47.7, 43.9, 52.3, 43.5, 57.6, 47.7, 48.5, 37.9, 42.4, 44.7, 40.2, 50.0, 48.5],
    'Landmark Position': [19.7, 37.8, 37.4, 27.9, 33.3, 36.8, 44.8, 49.5, 49.2, 35.5, 22.0, 25.1, 19.3, 23.2, 23.1, 19.9, 23.2, 22.9],
    'Goal Detection': [18.0, 28.7, 33.8, 27.8, 31.3, 42.4, 44.7, 16.7, 17.0, 37.2, 22.1, 28.4, 24.6, 26.5, 27.4, 28.1, 26.5, 28.1],
    'Cognitive Map': [21.9, 47.9, 46.0, 45.3, 42.4, 52.8, 61.1, 26.8, 52.1, 60.0, 64.2, 35.8, 39.2, 32.8, 28.3, 32.4, 34.7, 33.6],
    'Causal': [18.2, 60.0, 63.6, 62.7, 65.5, 66.4, 77.3, 44.5, 51.8, 64.5, 46.4, 55.5, 33.6, 36.4, 33.6, 32.7, 27.3, 30.9],
    'Counterfactual': [25.0, 42.4, 46.2, 24.2, 47.7, 44.7, 49.2, 20.5, 34.8, 42.4, 35.6, 29.5, 45.5, 35.6, 45.5, 44.7, 41.7, 43.2],
    'Association': [18.3, 20.0, 23.0, 17.8, 22.9, 45.8, 23.9, 58.7, 13.9, 19.1, 13.5, 11.7, 33.5, 24.8, 27.0, 26.5, 25.7, 27.4],
    'Progress Evaluation': [21.8, 43.3, 38.8, 39.2, 30.8, 34.2, 38.8, 36.6, 33.2, 32.5, 28.8, 33.9, 29.2, 29.5, 31.5, 28.9, 32.4, 31.3],
    'High-level Planning': [15.9, 32.6, 43.8, 48.4, 57.5, 67.8, 70.0, 52.3, 59.7, 41.9, 44.2, 59.3, 37.6, 32.2, 35.7, 37.6, 34.9, 34.5],
    'Action Generation': [16.4, 34.4, 31.9, 30.5, 25.4, 33.8, 29.6, 19.2, 15.6, 32.4, 27.3, 32.7, 20.9, 22.1, 21.4, 22.8, 22.3, 23.2]
}

# 创建DataFrame
df = pd.DataFrame(data)

# 移除非数值列，只保留指标列用于相关性分析
numeric_columns = df.columns[3:]  # 从第4列开始都是数值指标
numeric_df = df[numeric_columns].apply(pd.to_numeric, errors='coerce')

# 计算相关性矩阵
correlation_matrix = numeric_df.corr()

# 定义指标的完整名称和简化标识的映射
indicator_mapping = {
    'Trajectory Captioning': ('Trajectory Captioning', 'T1'),
    'Sequence Recall': ('Sequence Recall', 'T2'),
    'Object Recall': ('Object Recall', 'T3'),
    'Scene Recall': ('Scene Recall', 'T4'),
    'Spatial Position': ('Spatial Position', 'T5'),
    'Proximity': ('Proximity', 'T6'),
    'Duration': ('Duration', 'T7'),
    'Landmark Position': ('Landmark Position', 'T8'),
    'Goal Detection': ('Goal Detection', 'T9'),
    'Cognitive Map': ('Cognitive Map', 'T10'),
    'Causal': ('Causal', 'T11'),
    'Counterfactual': ('Counterfactual', 'T12'),
    'Association': ('Association', 'T13'),
    'Progress Evaluation': ('Progress Evaluation', 'T14'),
    'High-level Planning': ('High-level Planning', 'T15'),
    'Action Generation': ('Action Generation', 'T16')
}

# 创建标签
y_labels = [f"{full_name}({short_name})" for full_name, short_name in indicator_mapping.values()]
x_labels = [short_name for _, short_name in indicator_mapping.values()]

# 定义学术风格的颜色方案（降低饱和度）
colors = ['#000080', '#4B0082', '#8B008B', '#DC143C', '#FF4500', '#FFA500', '#FFD700', '#FFFF00']
n_bins = 100
# 创建更柔和的颜色映射
cmap = LinearSegmentedColormap.from_list('academic', colors, N=n_bins)
# 降低整体饱和度
cmap._init()
cmap._lut[:, -1] = 0.85  # 设置alpha值为0.85，降低饱和度

# 创建热力图
fig, ax = plt.subplots(figsize=(14, 11))
mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))  # 只显示下三角

# 绘制热力图
heatmap = sns.heatmap(correlation_matrix,
                     annot=False,  # 不显示数值
                     cmap=cmap,
                     vmin=-0.2, vmax=1.0,  # 根据参考图的颜色条范围
                     square=True,
                     linewidths=0.03,  # 减小线宽使图表更清洁
                     linecolor='white',
                     cbar_kws={
                         "shrink": 0.82,  # 颜色条高度与矩阵一致
                         "aspect": 30,   # 调整颜色条宽度比例
                         "pad": 0.02     # 颜色条与图表的间距
                     },
                     xticklabels=x_labels,
                     yticklabels=y_labels,
                     ax=ax)

# 设置标签字体大小
ax.set_xlabel('', fontsize=30)
ax.set_ylabel('', fontsize=30)

# 设置坐标轴标签字体大小和样式
ax.tick_params(axis='x', labelsize=28, labelrotation=45)
ax.tick_params(axis='y', labelsize=32, labelrotation=0)

# 设置x轴标签对齐方式
plt.setp(ax.get_xticklabels(), ha='right')

# 调整颜色条标签字体大小
cbar = heatmap.collections[0].colorbar
cbar.ax.tick_params(labelsize=32)  # 将字体大小设置为20

# 设置更紧凑的布局
plt.tight_layout()

# 保存图片
plt.savefig('correlation_heatmap.png', dpi=300, bbox_inches='tight')
plt.show()

print("相关性热力图已生成并保存为 'correlation_heatmap.png'")
print("\n相关性矩阵统计信息：")
print(f"最大相关性: {correlation_matrix.values.max():.3f}")
print(f"最小相关性: {correlation_matrix.values.min():.3f}")
print(f"平均相关性: {np.mean(correlation_matrix.values[correlation_matrix.values != 1.0]):.3f}")
