%!PS-Adobe-3.0 EPSF-3.0
%%LanguageLevel: 3
%%Title: model_performance_grouped_bar_chart.eps
%%Creator: Matplotlib v3.10.3, https://matplotlib.org/
%%CreationDate: Mon Jul 28 18:45:38 2025
%%Orientation: portrait
%%BoundingBox: 0 0 432 360
%%HiResBoundingBox: 0.000000 0.000000 431.541875 359.009836
%%EndComments
%%BeginProlog
/mpldict 9 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/sc { setcachedevice } _d
%!PS-Adobe-3.0 Resource-Font
%%Creator: Converted from TrueType to Type 3 by Matplotlib.
10 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix [0.00048828125 0 0 0.00048828125 0 0] def
/FontBBox [-1164 -628 4190 2129] def
/FontType 3 def
/Encoding [/uni00000003 /uni00000008 /uni0000000e /uni00000010 /uni00000011 /uni00000013 /uni00000014 /uni00000015 /uni00000016 /uni00000017 /uni00000018 /uni00000019 /uni0000001a /uni0000001b /uni00000024 /uni00000025 /uni0000002c /uni0000002f /uni00000036 /uni00000039 /uni00000044 /uni00000046 /uni00000048 /uni00000049 /uni0000004a /uni00000051 /uni00000052 /uni00000055 /uni00000057 /uni00000059] def
/CharStrings 31 dict dup begin
/.notdef 0 def
/uni00000003{512 0 0 0 0 0 sc
ce} _d
/uni00000008{1706 0 72 -56 1635 1387 sc
1392 1387 m
404 -56 l
315 -56 l
1303 1387 l
1392 1387 l

364 1387 m
454 1387 524 1350 573 1275 c
623 1200 648 1115 648 1018 c
648 902 620 812 564 749 c
508 686 441 654 362 654 c
309 654 261 668 217 697 c
173 726 138 770 111 827 c
85 884 72 948 72 1018 c
72 1088 85 1152 112 1211 c
139 1270 175 1314 221 1343 c
268 1372 315 1387 364 1387 c

361 1331 m
327 1331 297 1311 270 1271 c
244 1231 231 1147 231 1019 c
231 926 238 856 253 807 c
264 770 282 742 306 722 c
320 710 337 704 358 704 c
390 704 417 721 440 756 c
473 807 490 893 490 1013 c
490 1140 474 1229 441 1280 c
420 1314 393 1331 361 1331 c

1346 673 m
1393 673 1440 658 1487 628 c
1534 598 1571 554 1596 497 c
1622 440 1635 377 1635 308 c
1635 190 1607 100 1550 37 c
1493 -25 1426 -56 1349 -56 c
1300 -56 1253 -41 1207 -11 c
1162 19 1126 62 1099 117 c
1073 173 1060 237 1060 308 c
1060 378 1073 442 1099 500 c
1126 558 1162 601 1207 630 c
1253 659 1299 673 1346 673 c

1347 620 m
1315 620 1287 602 1264 566 c
1234 519 1219 431 1219 301 c
1219 182 1234 98 1265 51 c
1288 17 1315 0 1347 0 c
1378 0 1405 18 1429 55 c
1461 104 1477 187 1477 306 c
1477 431 1461 519 1429 569 c
1407 603 1380 620 1347 620 c

ce} _d
/uni0000000e{1155 0 37 141 1115 1219 sc
535 141 m
535 640 l
37 640 l
37 722 l
535 722 l
535 1219 l
615 1219 l
615 722 l
1115 722 l
1115 640 l
615 640 l
615 141 l
535 141 l

ce} _d
/uni00000010{682 0 83 384 600 535 sc
83 535 m
600 535 l
600 384 l
83 384 l
83 535 l

ce} _d
/uni00000011{512 0 145 -28 367 194 sc
256 194 m
287 194 314 183 335 161 c
356 140 367 114 367 83 c
367 52 356 26 334 4 c
313 -17 287 -28 256 -28 c
225 -28 199 -17 177 4 c
156 26 145 52 145 83 c
145 114 156 141 177 162 c
199 183 225 194 256 194 c

ce} _d
/uni00000013{1024 0 74 -24 951 1384 sc
74 670 m
74 825 97 958 144 1069 c
191 1181 253 1264 330 1319 c
390 1362 452 1384 516 1384 c
620 1384 713 1331 796 1225 c
899 1094 951 916 951 691 c
951 534 928 400 883 290 c
838 180 780 100 709 50 c
639 1 571 -24 506 -24 c
377 -24 269 52 183 205 c
110 334 74 489 74 670 c

270 645 m
270 458 293 306 339 188 c
377 89 434 39 509 39 c
545 39 582 55 621 87 c
660 120 689 174 709 250 c
740 365 755 526 755 735 c
755 890 739 1019 707 1122 c
683 1199 652 1253 614 1285 c
587 1307 554 1318 515 1318 c
470 1318 429 1298 394 1257 c
346 1202 313 1115 296 996 c
279 877 270 760 270 645 c

ce} _d
/uni00000014{1024 0 240 0 774 1384 sc
240 1223 m
570 1384 l
603 1384 l
603 239 l
603 163 606 116 612 97 c
619 78 632 64 652 54 c
672 44 713 38 774 37 c
774 0 l
264 0 l
264 37 l
328 38 369 44 388 53 c
407 63 420 76 427 92 c
434 109 438 158 438 239 c
438 971 l
438 1070 435 1133 428 1161 c
423 1182 415 1198 402 1208 c
390 1218 375 1223 358 1223 c
333 1223 299 1213 255 1192 c
240 1223 l

ce} _d
/uni00000015{1024 0 44 0 939 1384 sc
939 261 m
844 0 l
44 0 l
44 37 l
279 252 445 427 541 563 c
637 699 685 823 685 936 c
685 1022 659 1093 606 1148 c
553 1203 490 1231 417 1231 c
350 1231 290 1211 237 1172 c
184 1133 145 1076 120 1001 c
83 1001 l
100 1124 142 1219 211 1285 c
280 1351 367 1384 470 1384 c
580 1384 672 1349 745 1278 c
819 1207 856 1124 856 1028 c
856 959 840 891 808 822 c
759 714 679 600 568 479 c
402 298 298 188 257 151 c
611 151 l
683 151 733 154 762 159 c
791 164 818 175 841 191 c
864 208 885 231 902 261 c
939 261 l

ce} _d
/uni00000016{1024 0 83 -24 854 1384 sc
104 1098 m
143 1189 191 1260 250 1309 c
309 1359 383 1384 471 1384 c
580 1384 663 1349 721 1278 c
765 1225 787 1169 787 1109 c
787 1010 725 908 601 803 c
684 770 747 724 790 663 c
833 602 854 531 854 449 c
854 332 817 230 742 144 c
645 32 504 -24 319 -24 c
228 -24 165 -13 132 10 c
99 33 83 57 83 83 c
83 102 91 119 106 134 c
122 149 141 156 163 156 c
180 156 197 153 214 148 c
225 145 251 132 291 111 c
331 90 359 78 374 74 c
399 67 425 63 453 63 c
521 63 580 89 630 142 c
681 195 706 257 706 329 c
706 382 694 433 671 483 c
654 520 635 549 614 568 c
585 595 546 619 496 640 c
446 662 395 673 343 673 c
311 673 l
311 703 l
364 710 416 729 469 760 c
522 791 561 829 585 873 c
609 917 621 965 621 1018 c
621 1087 599 1142 556 1184 c
513 1227 460 1248 396 1248 c
293 1248 206 1193 137 1082 c
104 1098 l

ce} _d
/uni00000017{1024 0 32 0 953 1384 sc
953 500 m
953 358 l
771 358 l
771 0 l
606 0 l
606 358 l
32 358 l
32 486 l
661 1384 l
771 1384 l
771 500 l
953 500 l

606 500 m
606 1173 l
130 500 l
606 500 l

ce} _d
/uni00000018{1024 0 98 -24 889 1356 sc
889 1356 m
811 1186 l
403 1186 l
314 1004 l
491 978 631 912 734 807 c
823 716 867 610 867 487 c
867 416 852 350 823 289 c
794 228 758 177 714 134 c
670 91 621 57 567 31 c
490 -6 412 -24 331 -24 c
250 -24 190 -10 153 17 c
116 45 98 76 98 109 c
98 128 106 144 121 158 c
136 173 156 180 179 180 c
196 180 211 177 224 172 c
237 167 260 153 291 131 c
341 96 392 79 443 79 c
521 79 589 108 648 167 c
707 226 737 298 737 383 c
737 465 711 541 658 612 c
605 683 533 738 440 777 c
367 807 268 824 143 829 c
403 1356 l
889 1356 l

ce} _d
/uni00000019{1024 0 88 -24 945 1384 sc
918 1384 m
918 1347 l
830 1338 758 1321 702 1294 c
647 1268 592 1228 537 1174 c
483 1120 438 1060 402 993 c
367 927 337 848 313 757 c
409 823 505 856 602 856 c
695 856 775 819 843 744 c
911 669 945 573 945 456 c
945 343 911 239 842 146 c
759 33 650 -24 514 -24 c
421 -24 343 7 278 68 c
151 187 88 342 88 532 c
88 653 112 769 161 878 c
210 987 279 1084 369 1169 c
460 1254 546 1311 629 1340 c
712 1369 789 1384 860 1384 c
918 1384 l

296 684 m
284 594 278 521 278 466 c
278 402 290 332 313 257 c
337 182 372 123 419 79 c
453 48 494 32 543 32 c
601 32 653 59 698 114 c
744 169 767 247 767 348 c
767 462 744 561 699 644 c
654 727 589 769 506 769 c
481 769 453 764 424 753 c
395 742 353 719 296 684 c

ce} _d
/uni0000001a{1024 0 76 -28 933 1356 sc
206 1356 m
933 1356 l
933 1318 l
481 -28 l
369 -28 l
774 1193 l
401 1193 l
326 1193 272 1184 240 1166 c
184 1135 139 1088 105 1024 c
76 1035 l
206 1356 l

ce} _d
/uni0000001b{1024 0 124 -24 906 1384 sc
393 683 m
286 771 216 842 185 895 c
154 948 139 1004 139 1061 c
139 1149 173 1225 241 1288 c
309 1352 399 1384 512 1384 c
621 1384 709 1354 776 1295 c
843 1236 876 1168 876 1092 c
876 1041 858 990 822 937 c
786 884 711 822 597 751 c
714 660 792 589 830 537 c
881 469 906 397 906 322 c
906 227 870 145 797 77 c
724 10 629 -24 511 -24 c
382 -24 282 16 210 97 c
153 162 124 232 124 309 c
124 369 144 428 184 487 c
225 546 294 612 393 683 c

550 790 m
630 862 681 919 702 960 c
723 1002 734 1049 734 1102 c
734 1172 714 1227 675 1266 c
636 1306 582 1326 514 1326 c
446 1326 391 1306 348 1267 c
305 1228 284 1182 284 1129 c
284 1094 293 1060 310 1025 c
328 990 353 957 386 926 c
550 790 l

440 645 m
385 598 344 547 317 492 c
290 437 277 378 277 314 c
277 228 300 159 347 107 c
394 56 454 30 527 30 c
599 30 657 50 700 91 c
743 132 765 181 765 239 c
765 287 752 330 727 368 c
680 439 584 531 440 645 c

ce} _d
/uni00000024{1479 0 16 0 1456 1387 sc
937 454 m
412 454 l
320 240 l
297 187 286 148 286 122 c
286 101 296 83 315 67 c
335 52 378 42 443 37 c
443 0 l
16 0 l
16 37 l
73 47 109 60 126 76 c
160 108 198 173 239 271 c
716 1387 l
751 1387 l
1223 259 l
1261 168 1295 109 1326 82 c
1357 55 1401 40 1456 37 c
1456 0 l
921 0 l
921 37 l
975 40 1011 49 1030 64 c
1049 79 1059 98 1059 120 c
1059 149 1046 196 1019 259 c
937 454 l

909 528 m
679 1076 l
443 528 l
909 528 l

ce} _d
/uni00000025{1366 0 34 0 1254 1356 sc
946 692 m
1040 672 1110 640 1157 596 c
1222 535 1254 460 1254 371 c
1254 304 1233 239 1190 177 c
1147 116 1089 71 1014 42 c
940 14 827 0 674 0 c
34 0 l
34 37 l
85 37 l
142 37 182 55 207 91 c
222 114 230 164 230 240 c
230 1116 l
230 1200 220 1253 201 1275 c
175 1304 136 1319 85 1319 c
34 1319 l
34 1356 l
620 1356 l
729 1356 817 1348 883 1332 c
983 1308 1059 1265 1112 1204 c
1165 1143 1191 1073 1191 994 c
1191 926 1170 865 1129 811 c
1088 758 1027 718 946 692 c

422 746 m
447 741 475 738 506 735 c
538 733 573 732 611 732 c
708 732 781 742 830 763 c
879 784 917 817 943 860 c
969 903 982 951 982 1002 c
982 1081 950 1149 885 1205 c
820 1261 726 1289 602 1289 c
535 1289 475 1282 422 1267 c
422 746 l

422 98 m
499 80 576 71 651 71 c
772 71 864 98 927 152 c
990 207 1022 274 1022 354 c
1022 407 1008 457 979 506 c
950 555 904 593 839 621 c
774 649 694 663 599 663 c
558 663 522 662 493 661 c
464 660 440 657 422 654 c
422 98 l

ce} _d
/uni0000002c{682 0 51 0 632 1356 sc
632 37 m
632 0 l
51 0 l
51 37 l
99 37 l
155 37 196 53 221 86 c
237 107 245 159 245 240 c
245 1116 l
245 1185 241 1230 232 1252 c
225 1269 212 1283 191 1295 c
162 1311 131 1319 99 1319 c
51 1319 l
51 1356 l
632 1356 l
632 1319 l
583 1319 l
528 1319 487 1303 462 1270 c
445 1249 437 1197 437 1116 c
437 240 l
437 171 441 126 450 104 c
457 87 471 73 492 61 c
521 45 551 37 583 37 c
632 37 l

ce} _d
/uni0000002f{1251 0 41 0 1207 1356 sc
1174 375 m
1207 368 l
1091 0 l
41 0 l
41 37 l
92 37 l
149 37 190 56 215 93 c
229 114 236 164 236 241 c
236 1116 l
236 1201 227 1254 208 1275 c
182 1304 143 1319 92 1319 c
41 1319 l
41 1356 l
655 1356 l
655 1319 l
583 1320 532 1313 503 1299 c
474 1285 455 1267 444 1246 c
433 1225 428 1174 428 1093 c
428 241 l
428 186 433 148 444 127 c
452 113 464 103 481 96 c
498 89 550 86 637 86 c
736 86 l
840 86 913 94 955 109 c
997 124 1035 151 1070 190 c
1105 229 1139 291 1174 375 c

ce} _d
/uni00000036{1139 0 128 -31 1029 1387 sc
939 1387 m
939 918 l
902 918 l
890 1008 868 1080 837 1133 c
806 1186 762 1229 705 1260 c
648 1291 588 1307 527 1307 c
458 1307 400 1286 355 1243 c
310 1201 287 1153 287 1099 c
287 1058 301 1020 330 986 c
371 936 470 869 625 786 c
752 718 838 666 884 629 c
931 593 966 550 991 501 c
1016 452 1029 400 1029 346 c
1029 243 989 155 909 80 c
830 6 727 -31 602 -31 c
563 -31 526 -28 491 -22 c
470 -19 427 -7 362 14 c
297 35 256 46 239 46 c
222 46 209 41 199 31 c
190 21 183 0 178 -31 c
141 -31 l
141 434 l
178 434 l
195 337 219 264 248 215 c
277 167 322 127 382 95 c
443 63 509 47 581 47 c
664 47 730 69 778 113 c
827 157 851 209 851 269 c
851 302 842 336 823 370 c
805 404 777 436 738 465 c
712 485 641 527 525 592 c
409 657 326 709 277 748 c
228 787 191 829 166 876 c
141 923 128 974 128 1030 c
128 1127 165 1211 240 1281 c
315 1352 410 1387 525 1387 c
597 1387 673 1369 754 1334 c
791 1317 818 1309 833 1309 c
850 1309 864 1314 875 1324 c
886 1335 895 1356 902 1387 c
939 1387 l

ce} _d
/uni00000039{1479 0 18 -31 1454 1356 sc
1454 1356 m
1454 1319 l
1406 1310 1370 1295 1345 1273 c
1310 1240 1278 1190 1251 1123 c
778 -31 l
741 -31 l
233 1138 l
207 1198 189 1235 178 1248 c
161 1269 141 1285 116 1296 c
92 1308 59 1316 18 1319 c
18 1356 l
572 1356 l
572 1319 l
509 1313 469 1302 450 1287 c
431 1272 422 1252 422 1228 c
422 1195 437 1143 468 1072 c
813 277 l
1133 1062 l
1164 1139 1180 1193 1180 1223 c
1180 1242 1170 1261 1151 1278 c
1132 1296 1099 1309 1053 1316 c
1050 1317 1044 1318 1036 1319 c
1036 1356 l
1454 1356 l

ce} _d
/uni00000044{909 0 73 -19 905 943 sc
583 132 m
489 59 430 17 406 6 c
370 -11 332 -19 291 -19 c
228 -19 175 3 134 46 c
93 89 73 146 73 217 c
73 262 83 300 103 333 c
130 378 178 421 245 461 c
313 501 426 550 583 607 c
583 643 l
583 734 568 797 539 831 c
510 865 468 882 413 882 c
371 882 338 871 313 848 c
288 825 275 799 275 770 c
277 712 l
277 681 269 658 253 641 c
238 624 217 616 192 616 c
167 616 147 625 131 642 c
116 659 108 683 108 713 c
108 770 137 823 196 871 c
255 919 337 943 443 943 c
524 943 591 929 643 902 c
682 881 711 849 730 805 c
742 776 748 718 748 629 c
748 318 l
748 231 750 177 753 157 c
756 138 762 125 769 118 c
777 111 786 108 796 108 c
807 108 816 110 824 115 c
838 124 865 148 905 188 c
905 132 l
830 32 759 -18 691 -18 c
658 -18 632 -7 613 16 c
594 39 584 77 583 132 c

583 197 m
583 546 l
482 506 417 478 388 461 c
335 432 298 401 275 369 c
252 337 241 302 241 264 c
241 216 255 176 284 144 c
313 113 346 97 383 97 c
434 97 500 130 583 197 c

ce} _d
/uni00000046{909 0 70 -28 842 943 sc
842 348 m
817 227 769 134 697 69 c
625 4 545 -28 458 -28 c
354 -28 263 16 186 103 c
109 190 70 308 70 457 c
70 601 113 718 198 808 c
284 898 387 943 507 943 c
597 943 671 919 729 871 c
787 824 816 774 816 723 c
816 698 808 677 791 661 c
775 646 752 638 723 638 c
684 638 654 651 634 676 c
623 690 615 717 611 756 c
608 795 594 825 571 846 c
548 866 515 876 474 876 c
407 876 354 851 313 802 c
259 737 232 650 232 543 c
232 434 259 337 312 253 c
366 170 439 128 530 128 c
595 128 654 150 706 195 c
743 226 778 281 813 362 c
842 348 l

ce} _d
/uni00000048{909 0 76 -28 851 944 sc
218 571 m
217 435 250 328 317 251 c
384 174 462 135 552 135 c
612 135 664 151 708 184 c
753 217 790 274 820 354 c
851 334 l
837 243 796 159 729 84 c
662 9 577 -28 476 -28 c
366 -28 272 15 193 100 c
115 186 76 301 76 446 c
76 603 116 725 196 812 c
277 900 378 944 499 944 c
602 944 686 910 752 842 c
818 775 851 684 851 571 c
218 571 l

218 629 m
642 629 l
639 688 632 729 621 753 c
604 790 579 820 546 841 c
513 862 479 873 443 873 c
388 873 338 851 294 808 c
251 765 225 706 218 629 c

ce} _d
/uni00000049{682 0 79 0 890 1420 sc
422 844 m
422 242 l
422 157 431 103 450 80 c
475 51 508 36 549 36 c
632 36 l
632 0 l
85 0 l
85 36 l
126 36 l
153 36 177 43 199 56 c
221 69 236 87 244 110 c
253 133 257 177 257 242 c
257 844 l
79 844 l
79 916 l
257 916 l
257 976 l
257 1067 272 1145 301 1208 c
330 1271 375 1322 435 1361 c
496 1400 564 1420 639 1420 c
709 1420 773 1397 832 1352 c
871 1322 890 1288 890 1251 c
890 1231 881 1212 864 1194 c
847 1177 828 1168 808 1168 c
793 1168 776 1173 759 1184 c
742 1195 722 1219 697 1255 c
672 1292 650 1316 629 1329 c
608 1342 585 1348 560 1348 c
529 1348 503 1340 482 1323 c
461 1307 445 1282 436 1247 c
427 1213 422 1125 422 982 c
422 916 l
658 916 l
658 844 l
422 844 l

ce} _d
/uni0000004a{1024 0 61 -442 987 943 sc
309 334 m
253 361 210 399 180 448 c
150 497 135 552 135 611 c
135 702 169 780 237 845 c
306 910 393 943 500 943 c
587 943 663 922 727 879 c
921 879 l
950 879 966 878 971 876 c
976 875 979 872 981 868 c
985 862 987 851 987 836 c
987 819 985 807 982 800 c
980 797 976 794 971 792 c
966 790 950 789 921 789 c
802 789 l
839 741 858 680 858 605 c
858 520 825 447 760 386 c
695 325 607 295 497 295 c
452 295 405 302 358 315 c
329 290 309 267 298 248 c
288 229 283 213 283 200 c
283 189 288 178 299 167 c
310 156 332 149 364 144 c
383 141 429 139 504 137 c
641 134 730 129 771 123 c
833 114 882 91 919 54 c
956 17 975 -29 975 -84 c
975 -159 940 -230 869 -296 c
765 -393 629 -442 462 -442 c
333 -442 225 -413 136 -355 c
86 -322 61 -287 61 -251 c
61 -235 65 -219 72 -203 c
83 -178 107 -144 142 -100 c
147 -94 181 -58 244 8 c
209 29 185 47 170 63 c
156 80 149 98 149 119 c
149 142 158 170 177 201 c
196 232 240 277 309 334 c

483 895 m
434 895 392 875 359 836 c
326 797 309 736 309 655 c
309 550 332 468 377 410 c
412 366 456 344 509 344 c
560 344 601 363 634 401 c
667 439 683 499 683 580 c
683 686 660 769 614 829 c
580 873 536 895 483 895 c

299 0 m
268 -34 244 -66 228 -95 c
212 -124 204 -151 204 -176 c
204 -208 223 -236 262 -260 c
329 -301 425 -322 551 -322 c
671 -322 759 -301 816 -258 c
873 -216 902 -171 902 -123 c
902 -88 885 -64 851 -49 c
816 -34 748 -26 645 -23 c
495 -19 380 -11 299 0 c

ce} _d
/uni00000051{1024 0 12 0 1015 943 sc
331 749 m
438 878 541 943 638 943 c
688 943 731 930 767 905 c
803 880 832 839 853 782 c
868 742 875 681 875 598 c
875 207 l
875 149 880 110 889 89 c
896 72 908 59 924 50 c
941 41 971 36 1015 36 c
1015 0 l
562 0 l
562 36 l
581 36 l
624 36 653 42 670 55 c
687 68 699 88 706 113 c
709 123 710 154 710 207 c
710 582 l
710 665 699 726 677 763 c
656 801 619 820 568 820 c
489 820 410 777 331 690 c
331 207 l
331 145 335 107 342 92 c
351 73 364 58 380 49 c
397 40 430 36 480 36 c
480 0 l
27 0 l
27 36 l
47 36 l
94 36 125 48 141 71 c
158 95 166 140 166 207 c
166 547 l
166 657 163 724 158 748 c
153 772 146 788 135 797 c
125 806 111 810 94 810 c
75 810 53 805 27 795 c
12 831 l
288 943 l
331 943 l
331 749 l

ce} _d
/uni00000052{1024 0 69 -28 953 943 sc
512 943 m
651 943 762 890 846 785 c
917 695 953 592 953 475 c
953 393 933 310 894 226 c
855 142 800 79 731 36 c
662 -7 586 -28 501 -28 c
363 -28 253 27 172 137 c
103 230 69 334 69 449 c
69 533 90 616 131 699 c
173 782 228 844 296 883 c
364 923 436 943 512 943 c

481 878 m
446 878 410 867 374 846 c
339 825 310 789 288 736 c
266 683 255 616 255 533 c
255 400 281 285 334 188 c
387 91 457 43 544 43 c
609 43 662 70 704 123 c
746 176 767 268 767 398 c
767 561 732 689 662 782 c
615 846 554 878 481 878 c

ce} _d
/uni00000055{682 0 13 0 695 943 sc
332 943 m
332 737 l
409 874 487 943 568 943 c
605 943 635 932 659 909 c
683 887 695 861 695 832 c
695 806 686 784 669 766 c
652 748 631 739 607 739 c
584 739 557 750 528 773 c
499 796 478 808 464 808 c
452 808 439 801 425 788 c
395 761 364 716 332 653 c
332 214 l
332 163 338 125 351 99 c
360 81 375 66 397 54 c
419 42 451 36 492 36 c
492 0 l
23 0 l
23 36 l
70 36 104 43 127 58 c
144 69 155 86 162 109 c
165 120 167 153 167 206 c
167 561 l
167 668 165 731 160 751 c
156 772 148 787 136 796 c
125 805 110 810 93 810 c
72 810 49 805 23 795 c
13 831 l
290 943 l
332 943 l

ce} _d
/uni00000057{569 0 20 -15 572 1217 sc
330 1217 m
330 916 l
544 916 l
544 846 l
330 846 l
330 252 l
330 193 338 153 355 132 c
372 111 394 101 421 101 c
443 101 464 108 485 121 c
506 135 522 155 533 182 c
572 182 l
549 117 516 67 473 34 c
430 1 386 -15 341 -15 c
310 -15 280 -7 251 10 c
222 27 200 52 186 83 c
172 115 165 164 165 230 c
165 846 l
20 846 l
20 879 l
57 894 94 918 132 953 c
171 988 205 1030 235 1078 c
250 1103 272 1150 299 1217 c
330 1217 l

ce} _d
/uni00000059{1024 0 17 -28 1005 916 sc
17 916 m
448 916 l
448 879 l
420 879 l
394 879 374 873 360 860 c
347 847 340 830 340 809 c
340 786 347 758 361 726 c
574 220 l
788 745 l
803 782 811 811 811 830 c
811 839 808 847 803 853 c
796 863 786 870 775 873 c
764 877 741 879 706 879 c
706 916 l
1005 916 l
1005 879 l
970 876 946 869 933 858 c
910 838 889 805 870 758 c
545 -28 l
504 -28 l
177 745 l
162 781 148 807 135 822 c
122 838 105 851 84 862 c
73 868 50 874 17 879 c
17 916 l

ce} _d
end readonly def

/BuildGlyph {
 exch begin
 CharStrings exch
 2 copy known not {pop /.notdef} if
 true 3 1 roll get exec
 end
} _d

/BuildChar {
 1 index /Encoding get exch get
 1 index /BuildGlyph get exec
} _d

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
0 0 translate
0 0 431.542 359.01 rectclip
gsave
0 -0 m
431.541875 -0 l
431.541875 359.009836 l
0 359.009836 l
cl
1 setgray
fill
grestore
gsave
51.871875 44.825 m
424.341875 44.825 l
424.341875 351.809836 l
51.871875 351.809836 l
cl
1 setgray
fill
grestore
0.3 setlinewidth
1 setlinejoin
2 setlinecap
[] 0 setdash
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
132.291534 44.825 m
132.291534 351.809836 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
132.292 44.825 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

89.3853 10.4031 translate
0 rotate
0 0 m /uni0000002c glyphshow
4.99512 0 m /uni00000051 glyphshow
12.4951 0 m /uni00000057 glyphshow
16.6626 0 m /uni00000048 glyphshow
23.3203 0 m /uni00000055 glyphshow
28.3154 0 m /uni00000051 glyphshow
35.8154 0 m /uni00000039 glyphshow
46.6479 0 m /uni0000002f glyphshow
55.8105 0 m /uni00000015 glyphshow
63.3105 0 m /uni00000010 glyphshow
68.3057 0 m /uni00000017 glyphshow
75.8057 0 m /uni00000025 glyphshow
grestore
0.3 setlinewidth
2 setlinecap
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
343.922216 44.825 m
343.922216 351.809836 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
343.922 44.825 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

301.016 10.4031 translate
0 rotate
0 0 m /uni0000002c glyphshow
4.99512 0 m /uni00000051 glyphshow
12.4951 0 m /uni00000057 glyphshow
16.6626 0 m /uni00000048 glyphshow
23.3203 0 m /uni00000055 glyphshow
28.3154 0 m /uni00000051 glyphshow
35.8154 0 m /uni00000039 glyphshow
46.6479 0 m /uni0000002f glyphshow
55.8105 0 m /uni00000015 glyphshow
63.3105 0 m /uni00000010 glyphshow
68.3057 0 m /uni0000001b glyphshow
75.8057 0 m /uni00000025 glyphshow
grestore
0.3 setlinewidth
2 setlinecap
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
51.871875 44.825 m
424.341875 44.825 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
51.8719 44.825 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

36.8719 39.6141 translate
0 rotate
0 0 m /uni00000013 glyphshow
grestore
0.3 setlinewidth
2 setlinecap
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
51.871875 86.929627 m
424.341875 86.929627 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
51.8719 86.9296 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

36.8719 81.7187 translate
0 rotate
0 0 m /uni00000018 glyphshow
grestore
0.3 setlinewidth
2 setlinecap
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
51.871875 129.034254 m
424.341875 129.034254 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
51.8719 129.034 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

29.3719 123.823 translate
0 rotate
0 0 m /uni00000014 glyphshow
7.5 0 m /uni00000013 glyphshow
grestore
0.3 setlinewidth
2 setlinecap
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
51.871875 171.138881 m
424.341875 171.138881 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
51.8719 171.139 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

29.3719 165.928 translate
0 rotate
0 0 m /uni00000014 glyphshow
7.5 0 m /uni00000018 glyphshow
grestore
0.3 setlinewidth
2 setlinecap
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
51.871875 213.243508 m
424.341875 213.243508 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
51.8719 213.244 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

29.3719 208.033 translate
0 rotate
0 0 m /uni00000015 glyphshow
7.5 0 m /uni00000013 glyphshow
grestore
0.3 setlinewidth
2 setlinecap
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
51.871875 255.348135 m
424.341875 255.348135 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
51.8719 255.348 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

29.3719 250.137 translate
0 rotate
0 0 m /uni00000015 glyphshow
7.5 0 m /uni00000018 glyphshow
grestore
0.3 setlinewidth
2 setlinecap
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
51.871875 297.452763 m
424.341875 297.452763 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
51.8719 297.453 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

29.3719 292.242 translate
0 rotate
0 0 m /uni00000016 glyphshow
7.5 0 m /uni00000013 glyphshow
grestore
0.3 setlinewidth
2 setlinecap
0.502 setgray
gsave
51.872 44.825 372.47 306.985 rectclip
51.871875 339.55739 m
424.341875 339.55739 l
stroke
grestore
0.5 setlinewidth
0 setlinecap
0 setgray
gsave
/o {
gsave
newpath
translate
0.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
51.8719 339.557 o
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

29.3719 334.346 translate
0 rotate
0 0 m /uni00000016 glyphshow
7.5 0 m /uni00000018 glyphshow
grestore
/TimesNewRomanPSMT 20.000 selectfont
gsave

21.0594 139.942 translate
90 rotate
0 0 m /uni00000024 glyphshow
12.9434 0 m /uni00000059 glyphshow
22.9434 0 m /uni00000048 glyphshow
31.8203 0 m /uni00000055 glyphshow
38.4805 0 m /uni00000044 glyphshow
47.3574 0 m /uni0000004a glyphshow
57.3574 0 m /uni00000048 glyphshow
66.2344 0 m /uni00000003 glyphshow
71.2344 0 m /uni00000036 glyphshow
82.3574 0 m /uni00000046 glyphshow
91.2344 0 m /uni00000052 glyphshow
101.234 0 m /uni00000055 glyphshow
107.895 0 m /uni00000048 glyphshow
grestore
0 setlinejoin
gsave
51.872 44.825 372.47 306.985 rectclip
68.80233 44.825 m
132.291534 44.825 l
132.291534 283.137189 l
68.80233 283.137189 l
cl
gsave
0.267 0.447 0.769 setrgbcolor
fill
grestore
stroke
grestore
gsave
51.872 44.825 372.47 306.985 rectclip
280.433011 44.825 m
343.922216 44.825 l
343.922216 267.979524 l
280.433011 267.979524 l
cl
gsave
0.267 0.447 0.769 setrgbcolor
fill
grestore
stroke
grestore
gsave
51.872 44.825 372.47 306.985 rectclip
132.291534 44.825 m
195.780739 44.825 l
195.780739 310.084151 l
132.291534 310.084151 l
cl
gsave
0.882 0.341 0.349 setrgbcolor
fill
grestore
stroke
grestore
gsave
51.872 44.825 372.47 306.985 rectclip
343.922216 44.825 m
407.41142 44.825 l
407.41142 311.768336 l
343.922216 311.768336 l
cl
gsave
0.882 0.341 0.349 setrgbcolor
fill
grestore
stroke
grestore
2 setlinecap
gsave
51.871875 44.825 m
51.871875 351.809836 l
stroke
grestore
gsave
51.871875 44.825 m
424.341875 44.825 l
stroke
grestore
0.267 0.447 0.769 setrgbcolor
/TimesNewRomanPSMT 18.000 selectfont
gsave

76.0626 26.1853 translate
0 rotate
0 0 m /uni00000025 glyphshow
12.0059 0 m /uni00000048 glyphshow
19.9951 0 m /uni00000049 glyphshow
25.9893 0 m /uni00000052 glyphshow
34.9893 0 m /uni00000055 glyphshow
40.9834 0 m /uni00000048 glyphshow
grestore
0.882 0.341 0.349 setrgbcolor
/TimesNewRomanPSMT 18.000 selectfont
gsave

145.044 26.1853 translate
0 rotate
0 0 m /uni00000024 glyphshow
12.999 0 m /uni00000049 glyphshow
18.9932 0 m /uni00000057 glyphshow
23.9941 0 m /uni00000048 glyphshow
31.9834 0 m /uni00000055 glyphshow
grestore
0.267 0.447 0.769 setrgbcolor
/TimesNewRomanPSMT 18.000 selectfont
gsave

287.693 26.1853 translate
0 rotate
0 0 m /uni00000025 glyphshow
12.0059 0 m /uni00000048 glyphshow
19.9951 0 m /uni00000049 glyphshow
25.9893 0 m /uni00000052 glyphshow
34.9893 0 m /uni00000055 glyphshow
40.9834 0 m /uni00000048 glyphshow
grestore
0.882 0.341 0.349 setrgbcolor
/TimesNewRomanPSMT 18.000 selectfont
gsave

356.675 26.1853 translate
0 rotate
0 0 m /uni00000024 glyphshow
12.999 0 m /uni00000049 glyphshow
18.9932 0 m /uni00000057 glyphshow
23.9941 0 m /uni00000048 glyphshow
31.9834 0 m /uni00000055 glyphshow
grestore
0 setgray
/TimesNewRomanPSMT 15.000 selectfont
gsave

87.4219 288.34 translate
0 rotate
0 0 m /uni00000015 glyphshow
7.5 0 m /uni0000001b glyphshow
15 0 m /uni00000011 glyphshow
18.75 0 m /uni00000016 glyphshow
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

299.053 273.183 translate
0 rotate
0 0 m /uni00000015 glyphshow
7.5 0 m /uni00000019 glyphshow
15 0 m /uni00000011 glyphshow
18.75 0 m /uni00000018 glyphshow
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

150.911 315.287 translate
0 rotate
0 0 m /uni00000016 glyphshow
7.5 0 m /uni00000014 glyphshow
15 0 m /uni00000011 glyphshow
18.75 0 m /uni00000018 glyphshow
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

362.542 316.971 translate
0 rotate
0 0 m /uni00000016 glyphshow
7.5 0 m /uni00000014 glyphshow
15 0 m /uni00000011 glyphshow
18.75 0 m /uni0000001a glyphshow
grestore
0 setlinecap
0.18 0.545 0.341 setrgbcolor
gsave
112.439972 332.346927 m
152.143097 332.346927 l
154.143097 332.346927 155.143097 333.346927 155.143097 335.346927 c
155.143097 348.971927 l
155.143097 350.971927 154.143097 351.971927 152.143097 351.971927 c
112.439972 351.971927 l
110.439972 351.971927 109.439972 350.971927 109.439972 348.971927 c
109.439972 335.346927 l
109.439972 333.346927 110.439972 332.346927 112.439972 332.346927 c
112.439972 332.346927 l
cl
stroke
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

112.44 338.55 translate
0 rotate
0 0 m /uni0000000e glyphshow
8.45947 0 m /uni00000016 glyphshow
15.9595 0 m /uni00000011 glyphshow
19.7095 0 m /uni00000015 glyphshow
27.2095 0 m /uni00000008 glyphshow
grestore
gsave
324.070653 334.031112 m
363.773778 334.031112 l
365.773778 334.031112 366.773778 335.031112 366.773778 337.031112 c
366.773778 350.656112 l
366.773778 352.656112 365.773778 353.656112 363.773778 353.656112 c
324.070653 353.656112 l
322.070653 353.656112 321.070653 352.656112 321.070653 350.656112 c
321.070653 337.031112 l
321.070653 335.031112 322.070653 334.031112 324.070653 334.031112 c
324.070653 334.031112 l
cl
stroke
grestore
/TimesNewRomanPSMT 15.000 selectfont
gsave

324.071 340.234 translate
0 rotate
0 0 m /uni0000000e glyphshow
8.45947 0 m /uni00000018 glyphshow
15.9595 0 m /uni00000011 glyphshow
19.7095 0 m /uni00000015 glyphshow
27.2095 0 m /uni00000008 glyphshow
grestore

end
showpage
