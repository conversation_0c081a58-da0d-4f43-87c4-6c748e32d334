import matplotlib.pyplot as plt
import numpy as np

# 设置全局字体为学术期刊标准字体
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman', 'Times', 'DejaVu Serif']
plt.rcParams['mathtext.fontset'] = 'stix'  # 数学公式字体
plt.rcParams['font.size'] = 10  # 基础字体大小

# 从图片中提取的数据
methods = ['InternVL2-4B', 'InternVL2-8B']
before_values = [28.3, 26.5]  # 微调前的分数
after_values = [31.5, 31.7]   # 微调后的分数
improvement_percentages = [3.2, 5.2]  # 提升百分比

# 设置学术风格的图表 - 使用标准的单栏图尺寸
fig, ax = plt.subplots(figsize=(6, 4), dpi=300)

# 设置柱状图的位置
x = np.arange(len(methods))
width = 0.3  # 柱子的宽度，适中的宽度

# 学术期刊标准配色方案 - 使用灰度和深色调，适合黑白印刷
colors = {
    'before': '#4472C4',  # 深蓝色
    'after': '#E15759',   # 深红色
}

# 绘制分组柱状图，使用学术标准样式
bars1 = ax.bar(x - width/2, before_values, width, label='Before Fine-tuning',
               color=colors['before'], alpha=0.85, edgecolor='black', linewidth=0.5)
bars2 = ax.bar(x + width/2, after_values, width, label='After Fine-tuning',
               color=colors['after'], alpha=0.85, edgecolor='black', linewidth=0.5)

# 设置坐标轴标签 - 学术期刊标准
ax.set_ylabel('Performance Score', fontsize=16, fontweight='normal')  # y轴标签
ax.set_xticks(x)
ax.set_xticklabels(methods, fontsize=16)  # x轴刻度标签

# 设置刻度标签样式 - 更细致的控制
ax.tick_params(axis='both', which='major', labelsize=10, width=0.5, length=4)
ax.tick_params(axis='both', which='minor', width=0.3, length=2)

# 添加图例 - 学术期刊标准样式，采用坐标的形式设置位置
legend = ax.legend(
    fontsize=12, frameon=True, fancybox=False, shadow=False, edgecolor='black',
    bbox_to_anchor=(0.1, 1.00), loc='center', borderaxespad=0.2
)
legend.get_frame().set_linewidth(0.5)

# 在柱子顶部添加数值标签 - 学术期刊标准
def add_value_labels(bars):
    for bar in bars:
        height = bar.get_height()
        ax.annotate(f'{height:.1f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 2),  # 垂直方向偏移2个点
                    textcoords="offset points",
                    ha='center', va='bottom',
                    fontsize=12, fontweight='normal')

add_value_labels(bars1)
add_value_labels(bars2)

# 添加提升百分比的注释 - 更简洁的学术风格
for i, (method, improvement) in enumerate(zip(methods, improvement_percentages)):
    # 在较高的柱子上方显示提升百分比
    max_height = max(before_values[i], after_values[i])
    ax.annotate(f'+{improvement}%',
                xy=(i, max_height + 0.8),
                ha='center', va='bottom',
                fontsize=12, fontweight='normal',
                color='#2E8B57',  # 深绿色，更学术
                bbox=dict(boxstyle='round,pad=0.2', facecolor='none',
                         edgecolor='#2E8B57', linewidth=0.5))

# 设置网格 - 学术期刊标准：细致的网格线
ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.3, color='gray')
ax.set_axisbelow(True)

# 设置y轴范围，留出适当的顶部空间
y_max = max(max(before_values), max(after_values))
ax.set_ylim(0, y_max * 1.15)

# 设置坐标轴边框 - 学术期刊标准线宽
for spine in ax.spines.values():
    spine.set_linewidth(0.5)
    spine.set_color('black')

# 移除顶部和右侧边框，符合学术期刊惯例
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)

# 设置坐标轴刻度线样式
ax.tick_params(axis='x', direction='out', top=False)
ax.tick_params(axis='y', direction='out', right=False)

# 精确调整布局
plt.tight_layout(pad=0.5)

# 保存高分辨率图片 - 学术期刊标准
plt.savefig('model_performance_grouped_bar_chart.png',
            dpi=300, bbox_inches='tight', facecolor='white',
            edgecolor='none', format='png')
plt.savefig('model_performance_grouped_bar_chart.pdf',
            bbox_inches='tight', facecolor='white',
            edgecolor='none', format='pdf')
plt.savefig('model_performance_grouped_bar_chart.eps',
            bbox_inches='tight', facecolor='white',
            edgecolor='none', format='eps')

print("SCI-standard grouped bar chart has been created and saved as:")
print("- model_performance_grouped_bar_chart.png (300 DPI, publication ready)")
print("- model_performance_grouped_bar_chart.pdf (vector format)")
print("- model_performance_grouped_bar_chart.eps (vector format for LaTeX)")
print("\nChart specifications:")
print("- Font: Times New Roman (serif)")
print("- Resolution: 300 DPI")
print("- Color scheme: Academic journal standard")
print("- Line widths: 0.5pt (publication standard)")
print("- Layout: Single column format (6x4 inches)")

# 显示图表（可选）
# plt.show()
