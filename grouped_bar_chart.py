import matplotlib.pyplot as plt
import numpy as np

# 从图片中提取的数据
methods = ['InternVL2-4B', 'InternVL2-8B']
before_values = [28.3, 26.5]  # 微调前的分数
after_values = [31.5, 31.7]   # 微调后的分数
improvement_percentages = [3.2, 5.2]  # 提升百分比

# 设置学术风格的图表
plt.style.use('default')
fig, ax = plt.subplots(figsize=(10, 6))

# 设置柱状图的位置
x = np.arange(len(methods))
width = 0.25  # 柱子的宽度，减小宽度增加间距

# 绘制分组柱状图，使用学术常用配色（深蓝和橙色，色盲友好）
bars1 = ax.bar(x - width/2, before_values, width, label='Before Fine-tuning', 
               color='#1f77b4', alpha=0.8, edgecolor='black', linewidth=1)
bars2 = ax.bar(x + width/2, after_values, width, label='After Fine-tuning', 
               color='#ff7f0e', alpha=0.8, edgecolor='black', linewidth=1)

# 自定义图表
# ax.set_xlabel('Methods', fontsize=20, fontweight='bold')  # x轴标签
ax.set_ylabel('Performance Score', fontsize=25, fontweight='bold')  # y轴标签
# ax.set_title('Model Performance Comparison: Before vs After Fine-tuning', 
            #  fontsize=14, fontweight='bold', pad=20)  # 标题
ax.set_xticks(x)
ax.set_xticklabels(methods, fontsize=25)  # x轴刻度标签

# 设置Y轴刻度标签字体大小
ax.tick_params(axis='y', labelsize=20)

# 添加图例
ax.legend(loc='upper center', fontsize=20, frameon=True, fancybox=True, shadow=True)

# 在柱子顶部添加数值标签
def add_value_labels(bars):
    for bar in bars:
        height = bar.get_height()
        ax.annotate(f'{height:.1f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),  # 垂直方向偏移3个点
                    textcoords="offset points",
                    ha='center', va='bottom',
                    fontsize=20, fontweight='bold')

add_value_labels(bars1)
add_value_labels(bars2)

# 添加提升百分比的注释
for i, (method, improvement) in enumerate(zip(methods, improvement_percentages)):
    # 在较高的柱子上方显示提升百分比
    max_height = max(before_values[i], after_values[i])
    ax.annotate(f'+{improvement}%',
                xy=(i, max_height + 1.5),
                ha='center', va='bottom',
                fontsize=20, fontweight='bold',
                color='green',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7))

# 设置网格和坐标轴外观，增强学术感
ax.grid(True, alpha=0.3, linestyle='--')
ax.set_axisbelow(True)

# 设置y轴范围，留出一定的顶部空间
y_max = max(max(before_values), max(after_values))
ax.set_ylim(0, y_max * 1.2)

# 自定义坐标轴边框粗细
for spine in ax.spines.values():
    spine.set_linewidth(1.2)

# 自动调整布局，防止标签被遮挡
plt.tight_layout()

# 保存高分辨率图片和矢量图
plt.savefig('model_performance_grouped_bar_chart.png', dpi=300, bbox_inches='tight')
plt.savefig('model_performance_grouped_bar_chart.pdf', bbox_inches='tight')

# 显示图表
plt.show()

print("Grouped bar chart has been created and saved as:")
print("- model_performance_grouped_bar_chart.png (high resolution)")
print("- model_performance_grouped_bar_chart.pdf (vector format)")
