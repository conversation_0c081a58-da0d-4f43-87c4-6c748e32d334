---
type: "always_apply"
---

# Scratchpad

## 当前任务：基于图片数据生成相关性热力图

### 任务描述：
1. 从图片中提取模型性能数据
2. 计算各指标间的相关性系数
3. 绘制热力图，要求：
   - 使用与参考图一致的颜色方案
   - 纵坐标：完整指标名称(简化标识)
   - 横坐标：仅简化标识
   - 不显示数值，仅用颜色表示相关性

### 任务进度：
[x] 数据提取和整理
[x] 相关性计算
[x] 热力图绘制
[x] 代码优化和输出 - 调整颜色条、字体、饱和度等学术风格

### 完成的优化：
- 颜色条高度与矩阵完全一致 (shrink=1.0)
- 增大字体大小：坐标轴标签13pt，基础字体12pt
- 降低颜色饱和度：alpha=0.85，使用更柔和的学术色彩
- 优化线宽和间距，提升专业外观
- 生成高质量PNG图片 (300 DPI)

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- After writing the test code and ensuring all functions are correct, you should delete the test code files to avoid cluttering the project. Meanwhile, all test files should start with "Test_".
- No matter whether the user inputs in Chinese or English, you should default to replying in Chinese, unless the user requests you to reply in English.
- Please note that it is currently July 2025.
- Be sure to use an empty card for inference; before running memory-intensive programs, check the memory usage with nvidia-smi to avoid conflicts with other programs.

## augment learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
