---
type: "always_apply"
---

# Scratchpad

## 当前任务：优化grouped_bar_chart.py的SCI期刊绘图标准

### 任务描述：
优化 `grouped_bar_chart.py` 文件中的图表样式，使其符合SCI（科学期刊）论文的绘图标准：
1. 调整线条粗细为学术标准
2. 设置学术期刊标准字体（Times New Roman）
3. 采用学术期刊配色方案
4. 优化图表元素（坐标轴、图例、标注）
5. 确保高分辨率输出（300 DPI）

### 任务进度：
[x] 字体设置 - Times New Roman serif字体，数学公式STIX字体
[x] 图表尺寸 - 单栏标准尺寸 (6x4 inches)，300 DPI
[x] 配色方案 - 学术标准深蓝色和深红色，适合黑白印刷
[x] 线条优化 - 边框线宽0.5pt，刻度线细致控制
[x] 坐标轴优化 - 移除顶部和右侧边框，符合学术惯例
[x] 图例优化 - 简洁学术风格，无阴影效果
[x] 标注优化 - 数值标签和百分比标注使用合适字体大小
[x] 网格优化 - 细致网格线，透明度0.3
[x] 输出格式 - PNG(300DPI)、PDF、EPS三种格式

### 完成的SCI标准优化：
- 字体：Times New Roman serif，基础字体10pt
- 图表尺寸：6x4英寸，适合期刊单栏
- 线宽：边框0.5pt，刻度线0.5pt/0.3pt
- 配色：深蓝色#4472C4，深红色#E15759，透明度0.85
- 坐标轴：移除顶部和右侧边框
- 分辨率：300 DPI PNG + 矢量PDF/EPS格式
- 布局：紧凑学术布局，适当边距

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- After writing the test code and ensuring all functions are correct, you should delete the test code files to avoid cluttering the project. Meanwhile, all test files should start with "Test_".
- No matter whether the user inputs in Chinese or English, you should default to replying in Chinese, unless the user requests you to reply in English.
- Please note that it is currently July 2025.
- Be sure to use an empty card for inference; before running memory-intensive programs, check the memory usage with nvidia-smi to avoid conflicts with other programs.

## augment learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
